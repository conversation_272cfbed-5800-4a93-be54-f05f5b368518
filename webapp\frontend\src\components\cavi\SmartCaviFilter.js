import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  TextField,
  InputAdornment,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Chip,
  Button
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';

/**
 * Componente per il filtraggio intelligente dei cavi
 * Implementa un sistema di ricerca simile a quello delle bobine con supporto per termini multipli separati da virgola
 * 
 * @param {Object} props - Proprietà del componente
 * @param {Array} props.cavi - Lista completa dei cavi
 * @param {Function} props.onFilteredDataChange - Callback chiamata quando i dati filtrati cambiano
 * @param {boolean} props.loading - Indica se i dati sono in caricamento
 */
const SmartCaviFilter = ({ 
  cavi = [], 
  onFilteredDataChange = null, 
  loading = false 
}) => {
  const [searchText, setSearchText] = useState('');
  const [searchType, setSearchType] = useState('contains'); // 'contains' o 'equals'

  /**
   * Normalizza una stringa per la ricerca (lowercase, trim)
   */
  const normalizeString = (str) => {
    return String(str || '').toLowerCase().trim();
  };

  /**
   * Verifica se un termine numerico corrisponde a un valore
   */
  const matchesNumericTerm = (term, value) => {
    const numericTerm = parseFloat(term);
    const numericValue = parseFloat(String(value || '0'));
    
    if (isNaN(numericTerm) || isNaN(numericValue)) {
      return false;
    }
    
    return numericValue === numericTerm;
  };

  /**
   * Estrae informazioni dall'ID del cavo per facilitare la ricerca
   */
  const getCavoInfo = (idCavo) => {
    if (!idCavo) return { number: '', suffix: '', full: '' };

    // Estrae il numero finale (es. "CANT_001_C001" -> "001")
    const numberMatch = idCavo.match(/_C(\d+)$/);
    const number = numberMatch ? numberMatch[1] : '';

    // Estrae la parte finale completa (es. "CANT_001_C001" -> "C001")
    const suffixMatch = idCavo.match(/(C\d+)$/);
    const suffix = suffixMatch ? suffixMatch[1] : '';

    return {
      number: number,
      suffix: suffix,
      full: idCavo
    };
  };

  /**
   * Verifica se un cavo corrisponde a un termine di ricerca
   */
  const cavoMatchesTerm = (cavo, term, isExactMatch = false) => {
    const termStr = normalizeString(term);
    const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));

    // Campi da cercare
    const cavoInfo = getCavoInfo(cavo.id_cavo);
    const cavoId = normalizeString(cavoInfo.full);
    const cavoNumber = normalizeString(cavoInfo.number);
    const cavoSuffix = normalizeString(cavoInfo.suffix);
    const tipologia = normalizeString(cavo.tipologia);
    const sezione = normalizeString(cavo.sezione);
    const utility = normalizeString(cavo.utility);
    const sistema = normalizeString(cavo.sistema);
    const ubicazionePartenza = normalizeString(cavo.ubicazione_partenza);
    const ubicazioneArrivo = normalizeString(cavo.ubicazione_arrivo);
    const utenzaPartenza = normalizeString(cavo.utenza_partenza);
    const utenzaArrivo = normalizeString(cavo.utenza_arrivo);

    if (isExactMatch) {
      // Ricerca esatta
      if (isNumericTerm) {
        // Per termini numerici, verifica corrispondenza esatta con sezione e numero cavo
        return matchesNumericTerm(termStr, cavo.sezione) ||
               cavoNumber === termStr ||
               cavoId === termStr;
      } else {
        // Per termini testuali, verifica corrispondenza esatta
        return cavoId === termStr ||
               cavoNumber === termStr ||
               cavoSuffix === termStr ||
               tipologia === termStr ||
               sezione === termStr ||
               utility === termStr ||
               sistema === termStr ||
               ubicazionePartenza === termStr ||
               ubicazioneArrivo === termStr ||
               utenzaPartenza === termStr ||
               utenzaArrivo === termStr;
      }
    } else {
      // Ricerca con "contiene"
      if (isNumericTerm) {
        // Per termini numerici, verifica corrispondenza esatta con sezione
        if (matchesNumericTerm(termStr, cavo.sezione)) {
          return true;
        }
        // Verifica se il numero è contenuto nell'ID
        if (cavoId.includes(termStr) || cavoNumber.includes(termStr) || cavoSuffix.includes(termStr)) {
          return true;
        }
      }

      // Ricerca standard con includes
      return cavoId.includes(termStr) ||
             cavoNumber.includes(termStr) ||
             cavoSuffix.includes(termStr) ||
             tipologia.includes(termStr) ||
             sezione.includes(termStr) ||
             utility.includes(termStr) ||
             sistema.includes(termStr) ||
             ubicazionePartenza.includes(termStr) ||
             ubicazioneArrivo.includes(termStr) ||
             utenzaPartenza.includes(termStr) ||
             utenzaArrivo.includes(termStr);
    }
  };

  /**
   * Applica il filtro ai cavi
   */
  const applyFilter = useCallback(() => {
    if (!searchText.trim()) {
      // Se non c'è testo di ricerca, mostra tutti i cavi
      if (onFilteredDataChange) {
        onFilteredDataChange(cavi);
      }
      return;
    }

    // Dividi il testo di ricerca in termini separati da virgola
    const searchTerms = searchText.split(',')
      .map(term => term.trim())
      .filter(term => term.length > 0);

    console.log('SmartCaviFilter - Ricerca:', {
      searchText,
      searchType,
      searchTerms,
      totalCavi: cavi.length
    });

    let filtered = [];

    if (searchType === 'equals') {
      // Per la ricerca esatta con termini multipli, tutti i termini devono corrispondere (AND)
      if (searchTerms.length === 1) {
        // Singolo termine: ricerca esatta
        filtered = cavi.filter(cavo => {
          const matches = cavoMatchesTerm(cavo, searchTerms[0], true);
          if (matches) {
            console.log('SmartCaviFilter - Match trovato:', {
              cavo: cavo.id_cavo,
              term: searchTerms[0],
              cavoInfo: getCavoInfo(cavo.id_cavo)
            });
          }
          return matches;
        });
      } else {
        // Termini multipli: tutti devono corrispondere
        filtered = cavi.filter(cavo =>
          searchTerms.every(term => cavoMatchesTerm(cavo, term, true))
        );
      }
    } else {
      // Per la ricerca con 'contains', almeno un termine deve corrispondere (OR)
      filtered = cavi.filter(cavo =>
        searchTerms.some(term => cavoMatchesTerm(cavo, term, false))
      );
    }

    console.log('SmartCaviFilter - Risultati:', {
      filteredCount: filtered.length,
      filteredIds: filtered.map(c => c.id_cavo)
    });

    if (onFilteredDataChange) {
      onFilteredDataChange(filtered);
    }
  }, [searchText, searchType, cavi, onFilteredDataChange]);

  /**
   * Gestisce il cambio del testo di ricerca
   */
  const handleSearchTextChange = (event) => {
    setSearchText(event.target.value);
  };

  /**
   * Pulisce il filtro
   */
  const clearFilter = () => {
    setSearchText('');
    setSearchType('contains');
  };

  /**
   * Conta i termini di ricerca
   */
  const getSearchTermsCount = () => {
    if (!searchText.trim()) return 0;
    return searchText.split(',').map(term => term.trim()).filter(term => term.length > 0).length;
  };

  // Applica il filtro quando cambiano i parametri di ricerca o i dati
  useEffect(() => {
    applyFilter();
  }, [applyFilter]);

  return (
    <Box sx={{ mb: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
        {/* Campo di ricerca principale */}
        <TextField
          size="small"
          label="Ricerca intelligente cavi"
          variant="outlined"
          value={searchText}
          onChange={handleSearchTextChange}
          placeholder="ID, tipologia, formazione, utility, sistema, ubicazioni... (usa virgole per termini multipli)"
          disabled={loading}
          sx={{ flexGrow: 1, minWidth: '300px' }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon fontSize="small" />
              </InputAdornment>
            ),
            endAdornment: searchText ? (
              <InputAdornment position="end">
                <IconButton
                  size="small"
                  aria-label="clear search"
                  onClick={clearFilter}
                  edge="end"
                >
                  <CancelIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            ) : null
          }}
        />

        {/* Dropdown per il tipo di ricerca */}
        <FormControl size="small" sx={{ minWidth: '140px' }}>
          <InputLabel id="search-type-label">Tipo ricerca</InputLabel>
          <Select
            labelId="search-type-label"
            value={searchType}
            label="Tipo ricerca"
            onChange={(e) => setSearchType(e.target.value)}
            disabled={loading}
          >
            <MenuItem value="contains">Contiene</MenuItem>
            <MenuItem value="equals">Uguale a</MenuItem>
          </Select>
        </FormControl>

        {/* Pulsante per pulire tutti i filtri */}
        {searchText && (
          <Button
            variant="outlined"
            size="small"
            startIcon={<ClearIcon />}
            onClick={clearFilter}
            disabled={loading}
          >
            Pulisci
          </Button>
        )}
      </Box>

      {/* Informazioni sui risultati */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
        {/* Statistiche di ricerca */}
        <Typography variant="body2" color="text.secondary">
          {loading ? 'Caricamento...' : `Totale cavi: ${cavi.length}`}
        </Typography>

        {/* Chip con informazioni sui termini di ricerca */}
        {searchText && (
          <Chip
            size="small"
            label={`${getSearchTermsCount()} termine${getSearchTermsCount() > 1 ? 'i' : ''} di ricerca`}
            color="primary"
            variant="outlined"
          />
        )}

        {/* Chip con tipo di ricerca attivo */}
        {searchText && (
          <Chip
            size="small"
            label={searchType === 'contains' ? 'Ricerca per contenuto' : 'Ricerca esatta'}
            color="secondary"
            variant="outlined"
          />
        )}
      </Box>

      {/* Suggerimenti per l'uso */}
      {!searchText && (
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
          💡 Suggerimenti: Usa virgole per cercare più termini (es: "240,MT,PRYSMIAN"), 
          numeri per sezioni (es: "240"), testo per tipologie/utility/ubicazioni
        </Typography>
      )}
    </Box>
  );
};

export default SmartCaviFilter;
