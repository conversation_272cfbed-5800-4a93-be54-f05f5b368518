import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Typography,
  Chip,
  Button,
  CircularProgress,
  TablePagination
} from '@mui/material';
import { Clear as ClearIcon } from '@mui/icons-material';
import FilterableTableHeader from './FilterableTableHeader';

/**
 * Componente di tabella filtrabile in stile Excel
 *
 * @param {Object} props - Proprietà del componente
 * @param {Array} props.data - Dati da visualizzare
 * @param {Array} props.columns - Configurazione delle colonne
 * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano
 * @param {boolean} props.loading - Indica se i dati sono in caricamento
 * @param {string} props.emptyMessage - Messaggio da visualizzare quando non ci sono dati
 * @param {Function} props.renderRow - Funzione per renderizzare una riga personalizzata
 * @param {boolean} props.pagination - Abilita la paginazione
 * @param {Function} props.onResetFilters - Funzione chiamata quando vengono resettati i filtri
 */
const FilterableTable = ({
  data = [],
  columns = [],
  onFilteredDataChange = null,
  loading = false,
  emptyMessage = "Nessun dato disponibile",
  renderRow = null,
  pagination = true,
  onResetFilters = null
}) => {
  const [filteredData, setFilteredData] = useState(data);
  const [activeFilters, setActiveFilters] = useState({});
  const [sortConfig, setSortConfig] = useState({ key: null, direction: null });
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Aggiorna i dati filtrati quando cambiano i dati di input
  useEffect(() => {
    setFilteredData(data);
  }, [data]);

  // Notifica il componente padre quando cambiano i dati filtrati
  useEffect(() => {
    if (onFilteredDataChange) {
      onFilteredDataChange(filteredData);
    }
  }, [filteredData, onFilteredDataChange]);

  // Gestisce il cambio di filtro per una colonna
  const handleFilterChange = (columnName, filteredColumnData, filterConfig) => {
    // Aggiorna i filtri attivi
    setActiveFilters(prev => ({
      ...prev,
      [columnName]: filterConfig
    }));

    // Applica tutti i filtri attivi
    let result = [...data];

    // Per ogni filtro attivo, filtra i dati
    Object.keys(activeFilters).forEach(key => {
      if (key !== columnName) {
        const filter = activeFilters[key];
        result = applyFilter(result, filter);
      }
    });

    // Applica il nuovo filtro
    result = filteredColumnData;

    // Applica l'ordinamento corrente
    if (sortConfig.key) {
      result = applySorting(result, sortConfig.key, sortConfig.direction);
    }

    setFilteredData(result);
    setPage(0); // Torna alla prima pagina quando cambia il filtro
  };

  // Applica un filtro ai dati
  const applyFilter = (dataToFilter, filterConfig) => {
    if (!filterConfig) return dataToFilter;

    const { columnName, filterType, selectedValues, searchTerm, rangeValues } = filterConfig;

    let result = [...dataToFilter];

    // Applica il filtro in base al tipo
    if (filterType === 'equals' && selectedValues && selectedValues.length > 0) {
      result = result.filter(item => selectedValues.includes(item[columnName]));
    } else if (filterType === 'contains' && searchTerm) {
      result = result.filter(item =>
        item[columnName] && String(item[columnName]).toLowerCase().includes(searchTerm.toLowerCase())
      );
    } else if (filterType === 'greaterThan' && rangeValues && rangeValues.min !== '') {
      result = result.filter(item =>
        parseFloat(item[columnName]) > parseFloat(rangeValues.min)
      );
    } else if (filterType === 'lessThan' && rangeValues && rangeValues.max !== '') {
      result = result.filter(item =>
        parseFloat(item[columnName]) < parseFloat(rangeValues.max)
      );
    } else if (filterType === 'between' && rangeValues && rangeValues.min !== '' && rangeValues.max !== '') {
      result = result.filter(item =>
        parseFloat(item[columnName]) >= parseFloat(rangeValues.min) &&
        parseFloat(item[columnName]) <= parseFloat(rangeValues.max)
      );
    }

    return result;
  };

  // Gestisce il cambio di ordinamento
  const handleSortChange = (key, direction) => {
    setSortConfig({ key, direction });

    const sortedData = applySorting([...filteredData], key, direction);
    setFilteredData(sortedData);
  };

  // Applica l'ordinamento ai dati
  const applySorting = (dataToSort, key, direction) => {
    return dataToSort.sort((a, b) => {
      // Gestisci i valori null o undefined
      if (!a[key] && a[key] !== 0) return direction === 'asc' ? 1 : -1;
      if (!b[key] && b[key] !== 0) return direction === 'asc' ? -1 : 1;

      // Determina il tipo di dato
      const column = columns.find(col => col.field === key);
      const dataType = column?.dataType || 'text';

      let valueA = a[key];
      let valueB = b[key];

      // Converti i valori in base al tipo di dato
      if (dataType === 'number') {
        valueA = parseFloat(valueA) || 0;
        valueB = parseFloat(valueB) || 0;
      } else {
        valueA = String(valueA || '').toLowerCase();
        valueB = String(valueB || '').toLowerCase();
      }

      // Esegui l'ordinamento
      if (direction === 'asc') {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });
  };

  // Resetta tutti i filtri
  const resetAllFilters = () => {
    setActiveFilters({});
    setSortConfig({ key: null, direction: null });
    setFilteredData(data);
    setPage(0);

    // Chiama la callback onResetFilters se fornita
    if (onResetFilters) {
      onResetFilters();
    }
  };

  // Rimuove un filtro specifico
  const removeFilter = (columnName) => {
    const newFilters = { ...activeFilters };
    delete newFilters[columnName];
    setActiveFilters(newFilters);

    // Riapplica i filtri rimanenti
    let result = [...data];
    Object.values(newFilters).forEach(filter => {
      result = applyFilter(result, filter);
    });

    // Applica l'ordinamento corrente
    if (sortConfig.key) {
      result = applySorting(result, sortConfig.key, sortConfig.direction);
    }

    setFilteredData(result);
  };

  // Gestisce il cambio di pagina
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Gestisce il cambio di righe per pagina
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Calcola i dati da visualizzare in base alla paginazione
  const displayData = pagination
    ? filteredData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
    : filteredData;

  // Verifica se ci sono filtri attivi
  const hasActiveFilters = Object.keys(activeFilters).length > 0;

  return (
    <Box>
      {/* Mostra i filtri attivi */}
      {hasActiveFilters && (
        <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center' }}>
          <Typography variant="body2" sx={{ mr: 1 }}>Filtri attivi:</Typography>

          {Object.entries(activeFilters).map(([columnName, filter]) => {
            const column = columns.find(col => col.field === columnName);
            if (!column || !filter) return null;

            let label = '';
            if (filter.filterType === 'equals') {
              const selectedCount = filter.selectedValues?.length || 0;
              const totalCount = data.filter(item => item[columnName] !== undefined && item[columnName] !== null).length;
              label = `${column.headerName}: ${selectedCount} di ${totalCount}`;
            } else if (filter.filterType === 'contains') {
              label = `${column.headerName} contiene: ${filter.searchTerm}`;
            } else if (filter.filterType === 'greaterThan') {
              label = `${column.headerName} > ${filter.rangeValues.min}`;
            } else if (filter.filterType === 'lessThan') {
              label = `${column.headerName} < ${filter.rangeValues.max}`;
            } else if (filter.filterType === 'between') {
              label = `${column.headerName}: ${filter.rangeValues.min} - ${filter.rangeValues.max}`;
            }

            return (
              <Chip
                key={columnName}
                label={label}
                size="small"
                onDelete={() => removeFilter(columnName)}
              />
            );
          })}

          <Button
            variant="text"
            size="small"
            startIcon={<ClearIcon />}
            onClick={resetAllFilters}
          >
            Resetta tutti
          </Button>
        </Box>
      )}

      {/* Tabella */}
      <TableContainer component={Paper} sx={{ mb: 2 }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <FilterableTableHeader
                  key={column.field}
                  columnName={column.field}
                  label={column.headerName}
                  data={data}
                  onFilterChange={(filteredData, filterConfig) =>
                    handleFilterChange(column.field, filteredData, filterConfig)
                  }
                  dataType={column.dataType || 'text'}
                  sortDirection={sortConfig.key === column.field ? sortConfig.direction : null}
                  onSortChange={handleSortChange}
                  disableFilter={column.disableFilter}
                  disableSort={column.disableSort}
                  align={column.align || 'left'}
                  sx={column.headerStyle}
                />
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <td colSpan={columns.length} style={{ textAlign: 'center', padding: '20px' }}>
                  <CircularProgress size={30} />
                </td>
              </TableRow>
            ) : displayData.length > 0 ? (
              displayData.map((row, index) => (
                renderRow ? (
                  renderRow(row, index)
                ) : (
                  <TableRow key={index}>
                    {columns.map((column) => (
                      <td
                        key={column.field}
                        style={{
                          padding: '8px 16px',
                          textAlign: column.align || 'left',
                          ...(column.cellStyle || {})
                        }}
                      >
                        {column.renderCell ? column.renderCell(row) : row[column.field]}
                      </td>
                    ))}
                  </TableRow>
                )
              ))
            ) : (
              <TableRow>
                <td colSpan={columns.length} style={{ textAlign: 'center', padding: '20px' }}>
                  {emptyMessage}
                </td>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pulsante per resettare tutti i filtri spostato nel riquadro delle statistiche */}

      {/* Paginazione */}
      {pagination && filteredData.length > 0 && (
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50, 100]}
          component="div"
          count={filteredData.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Righe per pagina:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} di ${count}`}
        />
      )}
    </Box>
  );
};

export default FilterableTable;
