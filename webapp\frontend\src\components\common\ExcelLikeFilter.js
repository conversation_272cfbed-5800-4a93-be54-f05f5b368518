import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Popover,
  Typography,
  TextField,
  Checkbox,
  FormControlLabel,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  IconButton,
  InputAdornment,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  RadioGroup,
  Radio
} from '@mui/material';
import {
  FilterList as FilterIcon,
  Search as SearchIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Clear as ClearIcon
} from '@mui/icons-material';

/**
 * Componente di filtro in stile Excel
 * 
 * @param {Object} props - Proprietà del componente
 * @param {Array} props.data - <PERSON>ti da filtrare
 * @param {string} props.columnName - Nome della colonna da filtrare
 * @param {Function} props.onFilterChange - Funzione chiamata quando il filtro cambia
 * @param {string} props.dataType - Tipo di dati ('text', 'number', 'date')
 * @param {Array} props.uniqueValues - Valori unici per la colonna (opzionale)
 */
const ExcelLikeFilter = ({ 
  data, 
  columnName, 
  onFilterChange, 
  dataType = 'text',
  uniqueValues: propUniqueValues = null
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [uniqueValues, setUniqueValues] = useState([]);
  const [selectedValues, setSelectedValues] = useState([]);
  const [selectAll, setSelectAll] = useState(true);
  const [filterType, setFilterType] = useState('equals'); // equals, contains, greaterThan, lessThan, between
  const [rangeValues, setRangeValues] = useState({ min: '', max: '' });
  const [sortDirection, setSortDirection] = useState(null); // null, 'asc', 'desc'

  const inputRef = useRef(null);

  // Calcola i valori unici per la colonna
  useEffect(() => {
    if (propUniqueValues) {
      setUniqueValues(propUniqueValues);
      setSelectedValues(propUniqueValues);
      return;
    }

    if (data && data.length > 0) {
      const values = [...new Set(data.map(item => item[columnName]))].filter(Boolean);
      values.sort((a, b) => {
        if (dataType === 'number') {
          return parseFloat(a) - parseFloat(b);
        }
        return String(a).localeCompare(String(b));
      });
      setUniqueValues(values);
      setSelectedValues(values);
    }
  }, [data, columnName, propUniqueValues, dataType]);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleSelectAll = (event) => {
    const checked = event.target.checked;
    setSelectAll(checked);
    if (checked) {
      setSelectedValues(uniqueValues);
    } else {
      setSelectedValues([]);
    }
  };

  const handleValueSelect = (value) => {
    const newSelectedValues = selectedValues.includes(value)
      ? selectedValues.filter(v => v !== value)
      : [...selectedValues, value];

    setSelectedValues(newSelectedValues);
    setSelectAll(newSelectedValues.length === uniqueValues.length);
  };

  const handleFilterTypeChange = (event) => {
    setFilterType(event.target.value);
  };

  const handleRangeChange = (field, value) => {
    setRangeValues(prev => ({ ...prev, [field]: value }));
  };

  const handleSort = (direction) => {
    setSortDirection(direction);
    applyFilter(selectedValues, filterType, rangeValues, direction);
    handleClose();
  };

  const applyFilter = (values = selectedValues, type = filterType, range = rangeValues, sort = sortDirection) => {
    let filteredData = [...data];

    // Applica il filtro in base al tipo
    if (type === 'equals' && values.length < uniqueValues.length) {
      filteredData = filteredData.filter(item => values.includes(item[columnName]));
    } else if (type === 'contains' && searchTerm.trim()) {
      filteredData = filteredData.filter(item =>
        item[columnName] && String(item[columnName]).toLowerCase().includes(searchTerm.toLowerCase())
      );
    } else if (type === 'greaterThan' && range.min !== '') {
      filteredData = filteredData.filter(item =>
        parseFloat(item[columnName]) > parseFloat(range.min)
      );
    } else if (type === 'lessThan' && range.max !== '') {
      filteredData = filteredData.filter(item =>
        parseFloat(item[columnName]) < parseFloat(range.max)
      );
    } else if (type === 'between' && range.min !== '' && range.max !== '') {
      filteredData = filteredData.filter(item =>
        parseFloat(item[columnName]) >= parseFloat(range.min) &&
        parseFloat(item[columnName]) <= parseFloat(range.max)
      );
    }

    // Applica l'ordinamento
    if (sort) {
      filteredData.sort((a, b) => {
        let valueA = a[columnName];
        let valueB = b[columnName];

        if (dataType === 'number') {
          valueA = parseFloat(valueA) || 0;
          valueB = parseFloat(valueB) || 0;
        } else {
          valueA = String(valueA || '');
          valueB = String(valueB || '');
        }

        if (sort === 'asc') {
          return valueA > valueB ? 1 : -1;
        } else {
          return valueA < valueB ? 1 : -1;
        }
      });
    }

    onFilterChange(filteredData, {
      columnName,
      filterType: type,
      selectedValues: values,
      searchTerm,
      rangeValues: range,
      sortDirection: sort
    });
  };

  const handleApply = () => {
    applyFilter();
    handleClose();
  };

  const handleClear = () => {
    setSelectedValues(uniqueValues);
    setSelectAll(true);
    setFilterType('equals');
    setSearchTerm('');
    setRangeValues({ min: '', max: '' });
    setSortDirection(null);

    // Passa tutti i dati originali per resettare il filtro
    onFilterChange(data, null);

    handleClose();
  };

  const open = Boolean(anchorEl);
  const id = open ? `filter-popover-${columnName}` : undefined;

  // Filtra i valori unici in base al termine di ricerca
  const filteredUniqueValues = uniqueValues.filter(value => 
    String(value).toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Box>
      <IconButton 
        aria-describedby={id} 
        onClick={handleClick}
        size="small"
        color={
          (selectedValues.length < uniqueValues.length || 
           filterType !== 'equals' || 
           sortDirection) ? 'primary' : 'default'
        }
        sx={{
          position: 'relative',
          '&::after': (selectedValues.length < uniqueValues.length || 
                       filterType !== 'equals' || 
                       sortDirection) ? {
            content: '""',
            position: 'absolute',
            top: 2,
            right: 2,
            width: 8,
            height: 8,
            borderRadius: '50%',
            backgroundColor: '#f44336',
          } : {}
        }}
      >
        <FilterIcon fontSize="small" />
      </IconButton>

      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          style: { width: '300px', maxHeight: '500px' }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Filtro: {columnName}
          </Typography>

          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between' }}>
            <IconButton 
              size="small" 
              onClick={() => handleSort('asc')}
              color={sortDirection === 'asc' ? 'primary' : 'default'}
              title="Ordina crescente (A-Z)"
            >
              <ArrowUpwardIcon fontSize="small" />
            </IconButton>
            <IconButton 
              size="small" 
              onClick={() => handleSort('desc')}
              color={sortDirection === 'desc' ? 'primary' : 'default'}
              title="Ordina decrescente (Z-A)"
            >
              <ArrowDownwardIcon fontSize="small" />
            </IconButton>
            <IconButton 
              size="small" 
              onClick={handleClear}
              title="Cancella filtri"
            >
              <ClearIcon fontSize="small" />
            </IconButton>
          </Box>

          <Divider sx={{ mb: 2 }} />

          {dataType === 'text' && (
            <FormControl fullWidth size="small" sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ mb: 1 }}>Tipo di filtro:</Typography>
              <RadioGroup
                value={filterType}
                onChange={handleFilterTypeChange}
                row
              >
                <FormControlLabel 
                  value="equals" 
                  control={<Radio size="small" />} 
                  label="È uguale a" 
                />
                <FormControlLabel 
                  value="contains" 
                  control={<Radio size="small" />} 
                  label="Contiene" 
                />
              </RadioGroup>
            </FormControl>
          )}

          {dataType === 'number' && (
            <FormControl fullWidth size="small" sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ mb: 1 }}>Tipo di filtro:</Typography>
              <RadioGroup
                value={filterType}
                onChange={handleFilterTypeChange}
                sx={{ display: 'flex', flexDirection: 'column' }}
              >
                <FormControlLabel 
                  value="equals" 
                  control={<Radio size="small" />} 
                  label="È uguale a" 
                />
                <FormControlLabel 
                  value="greaterThan" 
                  control={<Radio size="small" />} 
                  label="Maggiore di" 
                />
                <FormControlLabel 
                  value="lessThan" 
                  control={<Radio size="small" />} 
                  label="Minore di" 
                />
                <FormControlLabel 
                  value="between" 
                  control={<Radio size="small" />} 
                  label="Compreso tra" 
                />
              </RadioGroup>
            </FormControl>
          )}

          {filterType === 'contains' && (
            <TextField
              fullWidth
              size="small"
              label="Cerca"
              variant="outlined"
              value={searchTerm}
              onChange={handleSearch}
              sx={{ mb: 2 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" />
                  </InputAdornment>
                ),
              }}
            />
          )}

          {filterType === 'greaterThan' && (
            <TextField
              fullWidth
              size="small"
              label="Valore minimo"
              variant="outlined"
              type="number"
              value={rangeValues.min}
              onChange={(e) => handleRangeChange('min', e.target.value)}
              sx={{ mb: 2 }}
            />
          )}

          {filterType === 'lessThan' && (
            <TextField
              fullWidth
              size="small"
              label="Valore massimo"
              variant="outlined"
              type="number"
              value={rangeValues.max}
              onChange={(e) => handleRangeChange('max', e.target.value)}
              sx={{ mb: 2 }}
            />
          )}

          {filterType === 'between' && (
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <TextField
                fullWidth
                size="small"
                label="Da"
                variant="outlined"
                type="number"
                value={rangeValues.min}
                onChange={(e) => handleRangeChange('min', e.target.value)}
              />
              <TextField
                fullWidth
                size="small"
                label="A"
                variant="outlined"
                type="number"
                value={rangeValues.max}
                onChange={(e) => handleRangeChange('max', e.target.value)}
              />
            </Box>
          )}

          {filterType === 'equals' && (
            <>
              <TextField
                fullWidth
                size="small"
                label="Cerca valori"
                variant="outlined"
                value={searchTerm}
                onChange={handleSearch}
                sx={{ mb: 2 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" />
                    </InputAdornment>
                  ),
                }}
                inputRef={inputRef}
              />

              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectAll}
                    onChange={handleSelectAll}
                    size="small"
                  />
                }
                label="Seleziona tutti"
                sx={{ mb: 1 }}
              />

              <Divider sx={{ mb: 1 }} />

              <List sx={{ maxHeight: '200px', overflow: 'auto', mb: 2 }}>
                {filteredUniqueValues.map((value, index) => (
                  <ListItem key={index} dense disablePadding>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={selectedValues.includes(value)}
                          onChange={() => handleValueSelect(value)}
                          size="small"
                        />
                      }
                      label={
                        <ListItemText 
                          primary={value || '(Vuoto)'} 
                          primaryTypographyProps={{ 
                            variant: 'body2',
                            style: { 
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap'
                            }
                          }}
                        />
                      }
                      sx={{ m: 0, width: '100%' }}
                    />
                  </ListItem>
                ))}
                {filteredUniqueValues.length === 0 && (
                  <ListItem>
                    <ListItemText 
                      primary="Nessun valore trovato" 
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                )}
              </List>
            </>
          )}

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button 
              variant="outlined" 
              size="small" 
              onClick={handleClose}
            >
              Annulla
            </Button>
            <Button 
              variant="contained" 
              size="small" 
              onClick={handleApply}
              color="primary"
            >
              Applica
            </Button>
          </Box>
        </Box>
      </Popover>
    </Box>
  );
};

export default ExcelLikeFilter;
