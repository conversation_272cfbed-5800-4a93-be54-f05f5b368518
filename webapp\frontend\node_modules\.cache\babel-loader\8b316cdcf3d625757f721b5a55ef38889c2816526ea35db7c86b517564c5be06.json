{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\common\\\\ExcelLikeFilter.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Box, Popover, Typography, TextField, Checkbox, FormControlLabel, Button, Divider, List, ListItem, ListItemText, IconButton, InputAdornment, MenuItem, Select, FormControl, InputLabel, RadioGroup, Radio } from '@mui/material';\nimport { FilterList as FilterIcon, Search as SearchIcon, ArrowUpward as ArrowUpwardIcon, ArrowDownward as ArrowDownwardIcon, Clear as ClearIcon } from '@mui/icons-material';\n\n/**\n * Componente di filtro in stile Excel\n * \n * @param {Object} props - Proprietà del componente\n * @param {Array} props.data - Dati da filtrare\n * @param {string} props.columnName - Nome della colonna da filtrare\n * @param {Function} props.onFilterChange - Funzione chiamata quando il filtro cambia\n * @param {string} props.dataType - Tipo di dati ('text', 'number', 'date')\n * @param {Array} props.uniqueValues - Valori unici per la colonna (opzionale)\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ExcelLikeFilter = ({\n  data,\n  columnName,\n  onFilterChange,\n  dataType = 'text',\n  uniqueValues: propUniqueValues = null\n}) => {\n  _s();\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [uniqueValues, setUniqueValues] = useState([]);\n  const [selectedValues, setSelectedValues] = useState([]);\n  const [selectAll, setSelectAll] = useState(true);\n  const [filterType, setFilterType] = useState('equals'); // equals, contains, greaterThan, lessThan, between\n  const [rangeValues, setRangeValues] = useState({\n    min: '',\n    max: ''\n  });\n  const [sortDirection, setSortDirection] = useState(null); // null, 'asc', 'desc'\n\n  const inputRef = useRef(null);\n\n  // Calcola i valori unici per la colonna\n  useEffect(() => {\n    if (propUniqueValues) {\n      setUniqueValues(propUniqueValues);\n      setSelectedValues(propUniqueValues);\n      return;\n    }\n    if (data && data.length > 0) {\n      const values = [...new Set(data.map(item => item[columnName]))].filter(Boolean);\n      values.sort((a, b) => {\n        if (dataType === 'number') {\n          return parseFloat(a) - parseFloat(b);\n        }\n        return String(a).localeCompare(String(b));\n      });\n      setUniqueValues(values);\n      setSelectedValues(values);\n    }\n  }, [data, columnName, propUniqueValues, dataType]);\n  const handleClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n  const handleSearch = event => {\n    setSearchTerm(event.target.value);\n  };\n  const handleSelectAll = event => {\n    const checked = event.target.checked;\n    setSelectAll(checked);\n    if (checked) {\n      setSelectedValues(uniqueValues);\n    } else {\n      setSelectedValues([]);\n    }\n  };\n  const handleValueSelect = value => {\n    const newSelectedValues = selectedValues.includes(value) ? selectedValues.filter(v => v !== value) : [...selectedValues, value];\n    setSelectedValues(newSelectedValues);\n    setSelectAll(newSelectedValues.length === uniqueValues.length);\n  };\n  const handleFilterTypeChange = event => {\n    setFilterType(event.target.value);\n  };\n  const handleRangeChange = (field, value) => {\n    setRangeValues(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleSort = direction => {\n    setSortDirection(direction);\n    applyFilter(selectedValues, filterType, rangeValues, direction);\n    handleClose();\n  };\n  const applyFilter = (values = selectedValues, type = filterType, range = rangeValues, sort = sortDirection) => {\n    let filteredData = [...data];\n\n    // Applica il filtro in base al tipo\n    if (type === 'equals' && values.length < uniqueValues.length) {\n      filteredData = filteredData.filter(item => values.includes(item[columnName]));\n    } else if (type === 'contains' && searchTerm.trim()) {\n      filteredData = filteredData.filter(item => item[columnName] && String(item[columnName]).toLowerCase().includes(searchTerm.toLowerCase()));\n    } else if (type === 'greaterThan' && range.min !== '') {\n      filteredData = filteredData.filter(item => parseFloat(item[columnName]) > parseFloat(range.min));\n    } else if (type === 'lessThan' && range.max !== '') {\n      filteredData = filteredData.filter(item => parseFloat(item[columnName]) < parseFloat(range.max));\n    } else if (type === 'between' && range.min !== '' && range.max !== '') {\n      filteredData = filteredData.filter(item => parseFloat(item[columnName]) >= parseFloat(range.min) && parseFloat(item[columnName]) <= parseFloat(range.max));\n    }\n\n    // Applica l'ordinamento\n    if (sort) {\n      filteredData.sort((a, b) => {\n        let valueA = a[columnName];\n        let valueB = b[columnName];\n        if (dataType === 'number') {\n          valueA = parseFloat(valueA) || 0;\n          valueB = parseFloat(valueB) || 0;\n        } else {\n          valueA = String(valueA || '');\n          valueB = String(valueB || '');\n        }\n        if (sort === 'asc') {\n          return valueA > valueB ? 1 : -1;\n        } else {\n          return valueA < valueB ? 1 : -1;\n        }\n      });\n    }\n    onFilterChange(filteredData, {\n      columnName,\n      filterType: type,\n      selectedValues: values,\n      searchTerm,\n      rangeValues: range,\n      sortDirection: sort\n    });\n  };\n  const handleApply = () => {\n    applyFilter();\n    handleClose();\n  };\n  const handleClear = () => {\n    setSelectedValues(uniqueValues);\n    setSelectAll(true);\n    setFilterType('equals');\n    setSearchTerm('');\n    setRangeValues({\n      min: '',\n      max: ''\n    });\n    setSortDirection(null);\n\n    // Passa tutti i dati originali per resettare il filtro\n    onFilterChange(data, {\n      columnName,\n      filterType: 'equals',\n      selectedValues: uniqueValues,\n      searchTerm: '',\n      rangeValues: {\n        min: '',\n        max: ''\n      },\n      sortDirection: null\n    });\n    handleClose();\n  };\n  const open = Boolean(anchorEl);\n  const id = open ? `filter-popover-${columnName}` : undefined;\n\n  // Filtra i valori unici in base al termine di ricerca\n  const filteredUniqueValues = uniqueValues.filter(value => String(value).toLowerCase().includes(searchTerm.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(IconButton, {\n      \"aria-describedby\": id,\n      onClick: handleClick,\n      size: \"small\",\n      color: selectedValues.length < uniqueValues.length || filterType !== 'equals' || sortDirection ? 'primary' : 'default',\n      sx: {\n        position: 'relative',\n        '&::after': selectedValues.length < uniqueValues.length || filterType !== 'equals' || sortDirection ? {\n          content: '\"\"',\n          position: 'absolute',\n          top: 2,\n          right: 2,\n          width: 8,\n          height: 8,\n          borderRadius: '50%',\n          backgroundColor: '#f44336'\n        } : {}\n      },\n      children: /*#__PURE__*/_jsxDEV(FilterIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      id: id,\n      open: open,\n      anchorEl: anchorEl,\n      onClose: handleClose,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'left'\n      },\n      transformOrigin: {\n        vertical: 'top',\n        horizontal: 'left'\n      },\n      PaperProps: {\n        style: {\n          width: '300px',\n          maxHeight: '500px'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: [\"Filtro: \", columnName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2,\n            display: 'flex',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: () => handleSort('asc'),\n            color: sortDirection === 'asc' ? 'primary' : 'default',\n            title: \"Ordina crescente (A-Z)\",\n            children: /*#__PURE__*/_jsxDEV(ArrowUpwardIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: () => handleSort('desc'),\n            color: sortDirection === 'desc' ? 'primary' : 'default',\n            title: \"Ordina decrescente (Z-A)\",\n            children: /*#__PURE__*/_jsxDEV(ArrowDownwardIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleClear,\n            title: \"Cancella filtri\",\n            children: /*#__PURE__*/_jsxDEV(ClearIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), dataType === 'text' && /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          size: \"small\",\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              mb: 1\n            },\n            children: \"Tipo di filtro:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n            value: filterType,\n            onChange: handleFilterTypeChange,\n            row: true,\n            children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n              value: \"equals\",\n              control: /*#__PURE__*/_jsxDEV(Radio, {\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 28\n              }, this),\n              label: \"\\xC8 uguale a\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              value: \"contains\",\n              control: /*#__PURE__*/_jsxDEV(Radio, {\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 28\n              }, this),\n              label: \"Contiene\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this), dataType === 'number' && /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          size: \"small\",\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              mb: 1\n            },\n            children: \"Tipo di filtro:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n            value: filterType,\n            onChange: handleFilterTypeChange,\n            sx: {\n              display: 'flex',\n              flexDirection: 'column'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n              value: \"equals\",\n              control: /*#__PURE__*/_jsxDEV(Radio, {\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 28\n              }, this),\n              label: \"\\xC8 uguale a\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              value: \"greaterThan\",\n              control: /*#__PURE__*/_jsxDEV(Radio, {\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 28\n              }, this),\n              label: \"Maggiore di\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              value: \"lessThan\",\n              control: /*#__PURE__*/_jsxDEV(Radio, {\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 28\n              }, this),\n              label: \"Minore di\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              value: \"between\",\n              control: /*#__PURE__*/_jsxDEV(Radio, {\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 28\n              }, this),\n              label: \"Compreso tra\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this), filterType === 'contains' && /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          size: \"small\",\n          label: \"Cerca\",\n          variant: \"outlined\",\n          value: searchTerm,\n          onChange: handleSearch,\n          sx: {\n            mb: 2\n          },\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 19\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this), filterType === 'greaterThan' && /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          size: \"small\",\n          label: \"Valore minimo\",\n          variant: \"outlined\",\n          type: \"number\",\n          value: rangeValues.min,\n          onChange: e => handleRangeChange('min', e.target.value),\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 13\n        }, this), filterType === 'lessThan' && /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          size: \"small\",\n          label: \"Valore massimo\",\n          variant: \"outlined\",\n          type: \"number\",\n          value: rangeValues.max,\n          onChange: e => handleRangeChange('max', e.target.value),\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this), filterType === 'between' && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1,\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            size: \"small\",\n            label: \"Da\",\n            variant: \"outlined\",\n            type: \"number\",\n            value: rangeValues.min,\n            onChange: e => handleRangeChange('min', e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            size: \"small\",\n            label: \"A\",\n            variant: \"outlined\",\n            type: \"number\",\n            value: rangeValues.max,\n            onChange: e => handleRangeChange('max', e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 13\n        }, this), filterType === 'equals' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            size: \"small\",\n            label: \"Cerca valori\",\n            variant: \"outlined\",\n            value: searchTerm,\n            onChange: handleSearch,\n            sx: {\n              mb: 2\n            },\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 21\n              }, this)\n            },\n            inputRef: inputRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: selectAll,\n              onChange: handleSelectAll,\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 19\n            }, this),\n            label: \"Seleziona tutti\",\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(List, {\n            sx: {\n              maxHeight: '200px',\n              overflow: 'auto',\n              mb: 2\n            },\n            children: [filteredUniqueValues.map((value, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n              dense: true,\n              disablePadding: true,\n              children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  checked: selectedValues.includes(value),\n                  onChange: () => handleValueSelect(value),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 25\n                }, this),\n                label: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: value || '(Vuoto)',\n                  primaryTypographyProps: {\n                    variant: 'body2',\n                    style: {\n                      overflow: 'hidden',\n                      textOverflow: 'ellipsis',\n                      whiteSpace: 'nowrap'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 25\n                }, this),\n                sx: {\n                  m: 0,\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 21\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 19\n            }, this)), filteredUniqueValues.length === 0 && /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Nessun valore trovato\",\n                primaryTypographyProps: {\n                  variant: 'body2'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'flex-end',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: handleClose,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            size: \"small\",\n            onClick: handleApply,\n            color: \"primary\",\n            children: \"Applica\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 217,\n    columnNumber: 5\n  }, this);\n};\n_s(ExcelLikeFilter, \"HjViF1Rw5QKfGpWSRnUImYFHIm4=\");\n_c = ExcelLikeFilter;\nexport default ExcelLikeFilter;\nvar _c;\n$RefreshReg$(_c, \"ExcelLikeFilter\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Popover", "Typography", "TextField", "Checkbox", "FormControlLabel", "<PERSON><PERSON>", "Divider", "List", "ListItem", "ListItemText", "IconButton", "InputAdornment", "MenuItem", "Select", "FormControl", "InputLabel", "RadioGroup", "Radio", "FilterList", "FilterIcon", "Search", "SearchIcon", "ArrowUpward", "ArrowUpwardIcon", "ArrowDownward", "ArrowDownwardIcon", "Clear", "ClearIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ExcelLikeFilter", "data", "columnName", "onFilterChange", "dataType", "uniqueValues", "propUniqueValues", "_s", "anchorEl", "setAnchorEl", "searchTerm", "setSearchTerm", "setUniqueValues", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedValues", "selectAll", "setSelectAll", "filterType", "setFilterType", "rangeValues", "setRangeV<PERSON>ues", "min", "max", "sortDirection", "setSortDirection", "inputRef", "length", "values", "Set", "map", "item", "filter", "Boolean", "sort", "a", "b", "parseFloat", "String", "localeCompare", "handleClick", "event", "currentTarget", "handleClose", "handleSearch", "target", "value", "handleSelectAll", "checked", "handleValueSelect", "newSelectedValues", "includes", "v", "handleFilterTypeChange", "handleRangeChange", "field", "prev", "handleSort", "direction", "applyFilter", "type", "range", "filteredData", "trim", "toLowerCase", "valueA", "valueB", "handleApply", "handleClear", "open", "id", "undefined", "filteredUniqueValues", "children", "onClick", "size", "color", "sx", "position", "content", "top", "right", "width", "height", "borderRadius", "backgroundColor", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "PaperProps", "style", "maxHeight", "p", "variant", "gutterBottom", "mb", "display", "justifyContent", "title", "fullWidth", "onChange", "row", "control", "label", "flexDirection", "InputProps", "startAdornment", "e", "gap", "overflow", "index", "dense", "disablePadding", "primary", "primaryTypographyProps", "textOverflow", "whiteSpace", "m", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/common/ExcelLikeFilter.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Box,\n  Popover,\n  Typography,\n  TextField,\n  Checkbox,\n  FormControlLabel,\n  Button,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  IconButton,\n  InputAdornment,\n  MenuItem,\n  Select,\n  FormControl,\n  InputLabel,\n  RadioGroup,\n  Radio\n} from '@mui/material';\nimport {\n  FilterList as FilterIcon,\n  Search as SearchIcon,\n  ArrowUpward as ArrowUpwardIcon,\n  ArrowDownward as ArrowDownwardIcon,\n  Clear as ClearIcon\n} from '@mui/icons-material';\n\n/**\n * Componente di filtro in stile Excel\n * \n * @param {Object} props - Proprietà del componente\n * @param {Array} props.data - <PERSON>ti da filtrare\n * @param {string} props.columnName - Nome della colonna da filtrare\n * @param {Function} props.onFilterChange - Funzione chiamata quando il filtro cambia\n * @param {string} props.dataType - Tipo di dati ('text', 'number', 'date')\n * @param {Array} props.uniqueValues - Valori unici per la colonna (opzionale)\n */\nconst ExcelLikeFilter = ({ \n  data, \n  columnName, \n  onFilterChange, \n  dataType = 'text',\n  uniqueValues: propUniqueValues = null\n}) => {\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [uniqueValues, setUniqueValues] = useState([]);\n  const [selectedValues, setSelectedValues] = useState([]);\n  const [selectAll, setSelectAll] = useState(true);\n  const [filterType, setFilterType] = useState('equals'); // equals, contains, greaterThan, lessThan, between\n  const [rangeValues, setRangeValues] = useState({ min: '', max: '' });\n  const [sortDirection, setSortDirection] = useState(null); // null, 'asc', 'desc'\n\n  const inputRef = useRef(null);\n\n  // Calcola i valori unici per la colonna\n  useEffect(() => {\n    if (propUniqueValues) {\n      setUniqueValues(propUniqueValues);\n      setSelectedValues(propUniqueValues);\n      return;\n    }\n\n    if (data && data.length > 0) {\n      const values = [...new Set(data.map(item => item[columnName]))].filter(Boolean);\n      values.sort((a, b) => {\n        if (dataType === 'number') {\n          return parseFloat(a) - parseFloat(b);\n        }\n        return String(a).localeCompare(String(b));\n      });\n      setUniqueValues(values);\n      setSelectedValues(values);\n    }\n  }, [data, columnName, propUniqueValues, dataType]);\n\n  const handleClick = (event) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n\n  const handleSearch = (event) => {\n    setSearchTerm(event.target.value);\n  };\n\n  const handleSelectAll = (event) => {\n    const checked = event.target.checked;\n    setSelectAll(checked);\n    if (checked) {\n      setSelectedValues(uniqueValues);\n    } else {\n      setSelectedValues([]);\n    }\n  };\n\n  const handleValueSelect = (value) => {\n    const newSelectedValues = selectedValues.includes(value)\n      ? selectedValues.filter(v => v !== value)\n      : [...selectedValues, value];\n\n    setSelectedValues(newSelectedValues);\n    setSelectAll(newSelectedValues.length === uniqueValues.length);\n  };\n\n  const handleFilterTypeChange = (event) => {\n    setFilterType(event.target.value);\n  };\n\n  const handleRangeChange = (field, value) => {\n    setRangeValues(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleSort = (direction) => {\n    setSortDirection(direction);\n    applyFilter(selectedValues, filterType, rangeValues, direction);\n    handleClose();\n  };\n\n  const applyFilter = (values = selectedValues, type = filterType, range = rangeValues, sort = sortDirection) => {\n    let filteredData = [...data];\n\n    // Applica il filtro in base al tipo\n    if (type === 'equals' && values.length < uniqueValues.length) {\n      filteredData = filteredData.filter(item => values.includes(item[columnName]));\n    } else if (type === 'contains' && searchTerm.trim()) {\n      filteredData = filteredData.filter(item =>\n        item[columnName] && String(item[columnName]).toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    } else if (type === 'greaterThan' && range.min !== '') {\n      filteredData = filteredData.filter(item =>\n        parseFloat(item[columnName]) > parseFloat(range.min)\n      );\n    } else if (type === 'lessThan' && range.max !== '') {\n      filteredData = filteredData.filter(item =>\n        parseFloat(item[columnName]) < parseFloat(range.max)\n      );\n    } else if (type === 'between' && range.min !== '' && range.max !== '') {\n      filteredData = filteredData.filter(item =>\n        parseFloat(item[columnName]) >= parseFloat(range.min) &&\n        parseFloat(item[columnName]) <= parseFloat(range.max)\n      );\n    }\n\n    // Applica l'ordinamento\n    if (sort) {\n      filteredData.sort((a, b) => {\n        let valueA = a[columnName];\n        let valueB = b[columnName];\n\n        if (dataType === 'number') {\n          valueA = parseFloat(valueA) || 0;\n          valueB = parseFloat(valueB) || 0;\n        } else {\n          valueA = String(valueA || '');\n          valueB = String(valueB || '');\n        }\n\n        if (sort === 'asc') {\n          return valueA > valueB ? 1 : -1;\n        } else {\n          return valueA < valueB ? 1 : -1;\n        }\n      });\n    }\n\n    onFilterChange(filteredData, {\n      columnName,\n      filterType: type,\n      selectedValues: values,\n      searchTerm,\n      rangeValues: range,\n      sortDirection: sort\n    });\n  };\n\n  const handleApply = () => {\n    applyFilter();\n    handleClose();\n  };\n\n  const handleClear = () => {\n    setSelectedValues(uniqueValues);\n    setSelectAll(true);\n    setFilterType('equals');\n    setSearchTerm('');\n    setRangeValues({ min: '', max: '' });\n    setSortDirection(null);\n\n    // Passa tutti i dati originali per resettare il filtro\n    onFilterChange(data, {\n      columnName,\n      filterType: 'equals',\n      selectedValues: uniqueValues,\n      searchTerm: '',\n      rangeValues: { min: '', max: '' },\n      sortDirection: null\n    });\n\n    handleClose();\n  };\n\n  const open = Boolean(anchorEl);\n  const id = open ? `filter-popover-${columnName}` : undefined;\n\n  // Filtra i valori unici in base al termine di ricerca\n  const filteredUniqueValues = uniqueValues.filter(value => \n    String(value).toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  return (\n    <Box>\n      <IconButton \n        aria-describedby={id} \n        onClick={handleClick}\n        size=\"small\"\n        color={\n          (selectedValues.length < uniqueValues.length || \n           filterType !== 'equals' || \n           sortDirection) ? 'primary' : 'default'\n        }\n        sx={{\n          position: 'relative',\n          '&::after': (selectedValues.length < uniqueValues.length || \n                       filterType !== 'equals' || \n                       sortDirection) ? {\n            content: '\"\"',\n            position: 'absolute',\n            top: 2,\n            right: 2,\n            width: 8,\n            height: 8,\n            borderRadius: '50%',\n            backgroundColor: '#f44336',\n          } : {}\n        }}\n      >\n        <FilterIcon fontSize=\"small\" />\n      </IconButton>\n\n      <Popover\n        id={id}\n        open={open}\n        anchorEl={anchorEl}\n        onClose={handleClose}\n        anchorOrigin={{\n          vertical: 'bottom',\n          horizontal: 'left',\n        }}\n        transformOrigin={{\n          vertical: 'top',\n          horizontal: 'left',\n        }}\n        PaperProps={{\n          style: { width: '300px', maxHeight: '500px' }\n        }}\n      >\n        <Box sx={{ p: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Filtro: {columnName}\n          </Typography>\n\n          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between' }}>\n            <IconButton \n              size=\"small\" \n              onClick={() => handleSort('asc')}\n              color={sortDirection === 'asc' ? 'primary' : 'default'}\n              title=\"Ordina crescente (A-Z)\"\n            >\n              <ArrowUpwardIcon fontSize=\"small\" />\n            </IconButton>\n            <IconButton \n              size=\"small\" \n              onClick={() => handleSort('desc')}\n              color={sortDirection === 'desc' ? 'primary' : 'default'}\n              title=\"Ordina decrescente (Z-A)\"\n            >\n              <ArrowDownwardIcon fontSize=\"small\" />\n            </IconButton>\n            <IconButton \n              size=\"small\" \n              onClick={handleClear}\n              title=\"Cancella filtri\"\n            >\n              <ClearIcon fontSize=\"small\" />\n            </IconButton>\n          </Box>\n\n          <Divider sx={{ mb: 2 }} />\n\n          {dataType === 'text' && (\n            <FormControl fullWidth size=\"small\" sx={{ mb: 2 }}>\n              <Typography variant=\"body2\" sx={{ mb: 1 }}>Tipo di filtro:</Typography>\n              <RadioGroup\n                value={filterType}\n                onChange={handleFilterTypeChange}\n                row\n              >\n                <FormControlLabel \n                  value=\"equals\" \n                  control={<Radio size=\"small\" />} \n                  label=\"È uguale a\" \n                />\n                <FormControlLabel \n                  value=\"contains\" \n                  control={<Radio size=\"small\" />} \n                  label=\"Contiene\" \n                />\n              </RadioGroup>\n            </FormControl>\n          )}\n\n          {dataType === 'number' && (\n            <FormControl fullWidth size=\"small\" sx={{ mb: 2 }}>\n              <Typography variant=\"body2\" sx={{ mb: 1 }}>Tipo di filtro:</Typography>\n              <RadioGroup\n                value={filterType}\n                onChange={handleFilterTypeChange}\n                sx={{ display: 'flex', flexDirection: 'column' }}\n              >\n                <FormControlLabel \n                  value=\"equals\" \n                  control={<Radio size=\"small\" />} \n                  label=\"È uguale a\" \n                />\n                <FormControlLabel \n                  value=\"greaterThan\" \n                  control={<Radio size=\"small\" />} \n                  label=\"Maggiore di\" \n                />\n                <FormControlLabel \n                  value=\"lessThan\" \n                  control={<Radio size=\"small\" />} \n                  label=\"Minore di\" \n                />\n                <FormControlLabel \n                  value=\"between\" \n                  control={<Radio size=\"small\" />} \n                  label=\"Compreso tra\" \n                />\n              </RadioGroup>\n            </FormControl>\n          )}\n\n          {filterType === 'contains' && (\n            <TextField\n              fullWidth\n              size=\"small\"\n              label=\"Cerca\"\n              variant=\"outlined\"\n              value={searchTerm}\n              onChange={handleSearch}\n              sx={{ mb: 2 }}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <SearchIcon fontSize=\"small\" />\n                  </InputAdornment>\n                ),\n              }}\n            />\n          )}\n\n          {filterType === 'greaterThan' && (\n            <TextField\n              fullWidth\n              size=\"small\"\n              label=\"Valore minimo\"\n              variant=\"outlined\"\n              type=\"number\"\n              value={rangeValues.min}\n              onChange={(e) => handleRangeChange('min', e.target.value)}\n              sx={{ mb: 2 }}\n            />\n          )}\n\n          {filterType === 'lessThan' && (\n            <TextField\n              fullWidth\n              size=\"small\"\n              label=\"Valore massimo\"\n              variant=\"outlined\"\n              type=\"number\"\n              value={rangeValues.max}\n              onChange={(e) => handleRangeChange('max', e.target.value)}\n              sx={{ mb: 2 }}\n            />\n          )}\n\n          {filterType === 'between' && (\n            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>\n              <TextField\n                fullWidth\n                size=\"small\"\n                label=\"Da\"\n                variant=\"outlined\"\n                type=\"number\"\n                value={rangeValues.min}\n                onChange={(e) => handleRangeChange('min', e.target.value)}\n              />\n              <TextField\n                fullWidth\n                size=\"small\"\n                label=\"A\"\n                variant=\"outlined\"\n                type=\"number\"\n                value={rangeValues.max}\n                onChange={(e) => handleRangeChange('max', e.target.value)}\n              />\n            </Box>\n          )}\n\n          {filterType === 'equals' && (\n            <>\n              <TextField\n                fullWidth\n                size=\"small\"\n                label=\"Cerca valori\"\n                variant=\"outlined\"\n                value={searchTerm}\n                onChange={handleSearch}\n                sx={{ mb: 2 }}\n                InputProps={{\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <SearchIcon fontSize=\"small\" />\n                    </InputAdornment>\n                  ),\n                }}\n                inputRef={inputRef}\n              />\n\n              <FormControlLabel\n                control={\n                  <Checkbox\n                    checked={selectAll}\n                    onChange={handleSelectAll}\n                    size=\"small\"\n                  />\n                }\n                label=\"Seleziona tutti\"\n                sx={{ mb: 1 }}\n              />\n\n              <Divider sx={{ mb: 1 }} />\n\n              <List sx={{ maxHeight: '200px', overflow: 'auto', mb: 2 }}>\n                {filteredUniqueValues.map((value, index) => (\n                  <ListItem key={index} dense disablePadding>\n                    <FormControlLabel\n                      control={\n                        <Checkbox\n                          checked={selectedValues.includes(value)}\n                          onChange={() => handleValueSelect(value)}\n                          size=\"small\"\n                        />\n                      }\n                      label={\n                        <ListItemText \n                          primary={value || '(Vuoto)'} \n                          primaryTypographyProps={{ \n                            variant: 'body2',\n                            style: { \n                              overflow: 'hidden',\n                              textOverflow: 'ellipsis',\n                              whiteSpace: 'nowrap'\n                            }\n                          }}\n                        />\n                      }\n                      sx={{ m: 0, width: '100%' }}\n                    />\n                  </ListItem>\n                ))}\n                {filteredUniqueValues.length === 0 && (\n                  <ListItem>\n                    <ListItemText \n                      primary=\"Nessun valore trovato\" \n                      primaryTypographyProps={{ variant: 'body2' }}\n                    />\n                  </ListItem>\n                )}\n              </List>\n            </>\n          )}\n\n          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>\n            <Button \n              variant=\"outlined\" \n              size=\"small\" \n              onClick={handleClose}\n            >\n              Annulla\n            </Button>\n            <Button \n              variant=\"contained\" \n              size=\"small\" \n              onClick={handleApply}\n              color=\"primary\"\n            >\n              Applica\n            </Button>\n          </Box>\n        </Box>\n      </Popover>\n    </Box>\n  );\n};\n\nexport default ExcelLikeFilter;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,cAAc,EACdC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SACEC,UAAU,IAAIC,UAAU,EACxBC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,aAAa,IAAIC,iBAAiB,EAClCC,KAAK,IAAIC,SAAS,QACb,qBAAqB;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAUA,MAAMC,eAAe,GAAGA,CAAC;EACvBC,IAAI;EACJC,UAAU;EACVC,cAAc;EACdC,QAAQ,GAAG,MAAM;EACjBC,YAAY,EAAEC,gBAAgB,GAAG;AACnC,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC8C,UAAU,EAAEC,aAAa,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyC,YAAY,EAAEO,eAAe,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmD,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACxD,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC;IAAEyD,GAAG,EAAE,EAAE;IAAEC,GAAG,EAAE;EAAG,CAAC,CAAC;EACpE,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE1D,MAAM6D,QAAQ,GAAG3D,MAAM,CAAC,IAAI,CAAC;;EAE7B;EACAD,SAAS,CAAC,MAAM;IACd,IAAIyC,gBAAgB,EAAE;MACpBM,eAAe,CAACN,gBAAgB,CAAC;MACjCQ,iBAAiB,CAACR,gBAAgB,CAAC;MACnC;IACF;IAEA,IAAIL,IAAI,IAAIA,IAAI,CAACyB,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMC,MAAM,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC3B,IAAI,CAAC4B,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC5B,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC6B,MAAM,CAACC,OAAO,CAAC;MAC/EL,MAAM,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACpB,IAAI/B,QAAQ,KAAK,QAAQ,EAAE;UACzB,OAAOgC,UAAU,CAACF,CAAC,CAAC,GAAGE,UAAU,CAACD,CAAC,CAAC;QACtC;QACA,OAAOE,MAAM,CAACH,CAAC,CAAC,CAACI,aAAa,CAACD,MAAM,CAACF,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC;MACFvB,eAAe,CAACe,MAAM,CAAC;MACvBb,iBAAiB,CAACa,MAAM,CAAC;IAC3B;EACF,CAAC,EAAE,CAAC1B,IAAI,EAAEC,UAAU,EAAEI,gBAAgB,EAAEF,QAAQ,CAAC,CAAC;EAElD,MAAMmC,WAAW,GAAIC,KAAK,IAAK;IAC7B/B,WAAW,CAAC+B,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBjC,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMkC,YAAY,GAAIH,KAAK,IAAK;IAC9B7B,aAAa,CAAC6B,KAAK,CAACI,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;EAED,MAAMC,eAAe,GAAIN,KAAK,IAAK;IACjC,MAAMO,OAAO,GAAGP,KAAK,CAACI,MAAM,CAACG,OAAO;IACpC/B,YAAY,CAAC+B,OAAO,CAAC;IACrB,IAAIA,OAAO,EAAE;MACXjC,iBAAiB,CAACT,YAAY,CAAC;IACjC,CAAC,MAAM;MACLS,iBAAiB,CAAC,EAAE,CAAC;IACvB;EACF,CAAC;EAED,MAAMkC,iBAAiB,GAAIH,KAAK,IAAK;IACnC,MAAMI,iBAAiB,GAAGpC,cAAc,CAACqC,QAAQ,CAACL,KAAK,CAAC,GACpDhC,cAAc,CAACkB,MAAM,CAACoB,CAAC,IAAIA,CAAC,KAAKN,KAAK,CAAC,GACvC,CAAC,GAAGhC,cAAc,EAAEgC,KAAK,CAAC;IAE9B/B,iBAAiB,CAACmC,iBAAiB,CAAC;IACpCjC,YAAY,CAACiC,iBAAiB,CAACvB,MAAM,KAAKrB,YAAY,CAACqB,MAAM,CAAC;EAChE,CAAC;EAED,MAAM0B,sBAAsB,GAAIZ,KAAK,IAAK;IACxCtB,aAAa,CAACsB,KAAK,CAACI,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;EAED,MAAMQ,iBAAiB,GAAGA,CAACC,KAAK,EAAET,KAAK,KAAK;IAC1CzB,cAAc,CAACmC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACD,KAAK,GAAGT;IAAM,CAAC,CAAC,CAAC;EACvD,CAAC;EAED,MAAMW,UAAU,GAAIC,SAAS,IAAK;IAChCjC,gBAAgB,CAACiC,SAAS,CAAC;IAC3BC,WAAW,CAAC7C,cAAc,EAAEI,UAAU,EAAEE,WAAW,EAAEsC,SAAS,CAAC;IAC/Df,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMgB,WAAW,GAAGA,CAAC/B,MAAM,GAAGd,cAAc,EAAE8C,IAAI,GAAG1C,UAAU,EAAE2C,KAAK,GAAGzC,WAAW,EAAEc,IAAI,GAAGV,aAAa,KAAK;IAC7G,IAAIsC,YAAY,GAAG,CAAC,GAAG5D,IAAI,CAAC;;IAE5B;IACA,IAAI0D,IAAI,KAAK,QAAQ,IAAIhC,MAAM,CAACD,MAAM,GAAGrB,YAAY,CAACqB,MAAM,EAAE;MAC5DmC,YAAY,GAAGA,YAAY,CAAC9B,MAAM,CAACD,IAAI,IAAIH,MAAM,CAACuB,QAAQ,CAACpB,IAAI,CAAC5B,UAAU,CAAC,CAAC,CAAC;IAC/E,CAAC,MAAM,IAAIyD,IAAI,KAAK,UAAU,IAAIjD,UAAU,CAACoD,IAAI,CAAC,CAAC,EAAE;MACnDD,YAAY,GAAGA,YAAY,CAAC9B,MAAM,CAACD,IAAI,IACrCA,IAAI,CAAC5B,UAAU,CAAC,IAAImC,MAAM,CAACP,IAAI,CAAC5B,UAAU,CAAC,CAAC,CAAC6D,WAAW,CAAC,CAAC,CAACb,QAAQ,CAACxC,UAAU,CAACqD,WAAW,CAAC,CAAC,CAC9F,CAAC;IACH,CAAC,MAAM,IAAIJ,IAAI,KAAK,aAAa,IAAIC,KAAK,CAACvC,GAAG,KAAK,EAAE,EAAE;MACrDwC,YAAY,GAAGA,YAAY,CAAC9B,MAAM,CAACD,IAAI,IACrCM,UAAU,CAACN,IAAI,CAAC5B,UAAU,CAAC,CAAC,GAAGkC,UAAU,CAACwB,KAAK,CAACvC,GAAG,CACrD,CAAC;IACH,CAAC,MAAM,IAAIsC,IAAI,KAAK,UAAU,IAAIC,KAAK,CAACtC,GAAG,KAAK,EAAE,EAAE;MAClDuC,YAAY,GAAGA,YAAY,CAAC9B,MAAM,CAACD,IAAI,IACrCM,UAAU,CAACN,IAAI,CAAC5B,UAAU,CAAC,CAAC,GAAGkC,UAAU,CAACwB,KAAK,CAACtC,GAAG,CACrD,CAAC;IACH,CAAC,MAAM,IAAIqC,IAAI,KAAK,SAAS,IAAIC,KAAK,CAACvC,GAAG,KAAK,EAAE,IAAIuC,KAAK,CAACtC,GAAG,KAAK,EAAE,EAAE;MACrEuC,YAAY,GAAGA,YAAY,CAAC9B,MAAM,CAACD,IAAI,IACrCM,UAAU,CAACN,IAAI,CAAC5B,UAAU,CAAC,CAAC,IAAIkC,UAAU,CAACwB,KAAK,CAACvC,GAAG,CAAC,IACrDe,UAAU,CAACN,IAAI,CAAC5B,UAAU,CAAC,CAAC,IAAIkC,UAAU,CAACwB,KAAK,CAACtC,GAAG,CACtD,CAAC;IACH;;IAEA;IACA,IAAIW,IAAI,EAAE;MACR4B,YAAY,CAAC5B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1B,IAAI6B,MAAM,GAAG9B,CAAC,CAAChC,UAAU,CAAC;QAC1B,IAAI+D,MAAM,GAAG9B,CAAC,CAACjC,UAAU,CAAC;QAE1B,IAAIE,QAAQ,KAAK,QAAQ,EAAE;UACzB4D,MAAM,GAAG5B,UAAU,CAAC4B,MAAM,CAAC,IAAI,CAAC;UAChCC,MAAM,GAAG7B,UAAU,CAAC6B,MAAM,CAAC,IAAI,CAAC;QAClC,CAAC,MAAM;UACLD,MAAM,GAAG3B,MAAM,CAAC2B,MAAM,IAAI,EAAE,CAAC;UAC7BC,MAAM,GAAG5B,MAAM,CAAC4B,MAAM,IAAI,EAAE,CAAC;QAC/B;QAEA,IAAIhC,IAAI,KAAK,KAAK,EAAE;UAClB,OAAO+B,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC,CAAC,MAAM;UACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC;MACF,CAAC,CAAC;IACJ;IAEA9D,cAAc,CAAC0D,YAAY,EAAE;MAC3B3D,UAAU;MACVe,UAAU,EAAE0C,IAAI;MAChB9C,cAAc,EAAEc,MAAM;MACtBjB,UAAU;MACVS,WAAW,EAAEyC,KAAK;MAClBrC,aAAa,EAAEU;IACjB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiC,WAAW,GAAGA,CAAA,KAAM;IACxBR,WAAW,CAAC,CAAC;IACbhB,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMyB,WAAW,GAAGA,CAAA,KAAM;IACxBrD,iBAAiB,CAACT,YAAY,CAAC;IAC/BW,YAAY,CAAC,IAAI,CAAC;IAClBE,aAAa,CAAC,QAAQ,CAAC;IACvBP,aAAa,CAAC,EAAE,CAAC;IACjBS,cAAc,CAAC;MAAEC,GAAG,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAG,CAAC,CAAC;IACpCE,gBAAgB,CAAC,IAAI,CAAC;;IAEtB;IACArB,cAAc,CAACF,IAAI,EAAE;MACnBC,UAAU;MACVe,UAAU,EAAE,QAAQ;MACpBJ,cAAc,EAAER,YAAY;MAC5BK,UAAU,EAAE,EAAE;MACdS,WAAW,EAAE;QAAEE,GAAG,EAAE,EAAE;QAAEC,GAAG,EAAE;MAAG,CAAC;MACjCC,aAAa,EAAE;IACjB,CAAC,CAAC;IAEFmB,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAM0B,IAAI,GAAGpC,OAAO,CAACxB,QAAQ,CAAC;EAC9B,MAAM6D,EAAE,GAAGD,IAAI,GAAG,kBAAkBlE,UAAU,EAAE,GAAGoE,SAAS;;EAE5D;EACA,MAAMC,oBAAoB,GAAGlE,YAAY,CAAC0B,MAAM,CAACc,KAAK,IACpDR,MAAM,CAACQ,KAAK,CAAC,CAACkB,WAAW,CAAC,CAAC,CAACb,QAAQ,CAACxC,UAAU,CAACqD,WAAW,CAAC,CAAC,CAC/D,CAAC;EAED,oBACElE,OAAA,CAAC9B,GAAG;IAAAyG,QAAA,gBACF3E,OAAA,CAACnB,UAAU;MACT,oBAAkB2F,EAAG;MACrBI,OAAO,EAAElC,WAAY;MACrBmC,IAAI,EAAC,OAAO;MACZC,KAAK,EACF9D,cAAc,CAACa,MAAM,GAAGrB,YAAY,CAACqB,MAAM,IAC3CT,UAAU,KAAK,QAAQ,IACvBM,aAAa,GAAI,SAAS,GAAG,SAC/B;MACDqD,EAAE,EAAE;QACFC,QAAQ,EAAE,UAAU;QACpB,UAAU,EAAGhE,cAAc,CAACa,MAAM,GAAGrB,YAAY,CAACqB,MAAM,IAC3CT,UAAU,KAAK,QAAQ,IACvBM,aAAa,GAAI;UAC5BuD,OAAO,EAAE,IAAI;UACbD,QAAQ,EAAE,UAAU;UACpBE,GAAG,EAAE,CAAC;UACNC,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTC,YAAY,EAAE,KAAK;UACnBC,eAAe,EAAE;QACnB,CAAC,GAAG,CAAC;MACP,CAAE;MAAAZ,QAAA,eAEF3E,OAAA,CAACV,UAAU;QAACkG,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eAEb5F,OAAA,CAAC7B,OAAO;MACNqG,EAAE,EAAEA,EAAG;MACPD,IAAI,EAAEA,IAAK;MACX5D,QAAQ,EAAEA,QAAS;MACnBkF,OAAO,EAAEhD,WAAY;MACrBiD,YAAY,EAAE;QACZC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACd,CAAE;MACFC,eAAe,EAAE;QACfF,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACd,CAAE;MACFE,UAAU,EAAE;QACVC,KAAK,EAAE;UAAEf,KAAK,EAAE,OAAO;UAAEgB,SAAS,EAAE;QAAQ;MAC9C,CAAE;MAAAzB,QAAA,eAEF3E,OAAA,CAAC9B,GAAG;QAAC6G,EAAE,EAAE;UAAEsB,CAAC,EAAE;QAAE,CAAE;QAAA1B,QAAA,gBAChB3E,OAAA,CAAC5B,UAAU;UAACkI,OAAO,EAAC,WAAW;UAACC,YAAY;UAAA5B,QAAA,GAAC,UACnC,EAACtE,UAAU;QAAA;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEb5F,OAAA,CAAC9B,GAAG;UAAC6G,EAAE,EAAE;YAAEyB,EAAE,EAAE,CAAC;YAAEC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAgB,CAAE;UAAA/B,QAAA,gBACnE3E,OAAA,CAACnB,UAAU;YACTgG,IAAI,EAAC,OAAO;YACZD,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,KAAK,CAAE;YACjCmB,KAAK,EAAEpD,aAAa,KAAK,KAAK,GAAG,SAAS,GAAG,SAAU;YACvDiF,KAAK,EAAC,wBAAwB;YAAAhC,QAAA,eAE9B3E,OAAA,CAACN,eAAe;cAAC8F,QAAQ,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACb5F,OAAA,CAACnB,UAAU;YACTgG,IAAI,EAAC,OAAO;YACZD,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,MAAM,CAAE;YAClCmB,KAAK,EAAEpD,aAAa,KAAK,MAAM,GAAG,SAAS,GAAG,SAAU;YACxDiF,KAAK,EAAC,0BAA0B;YAAAhC,QAAA,eAEhC3E,OAAA,CAACJ,iBAAiB;cAAC4F,QAAQ,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACb5F,OAAA,CAACnB,UAAU;YACTgG,IAAI,EAAC,OAAO;YACZD,OAAO,EAAEN,WAAY;YACrBqC,KAAK,EAAC,iBAAiB;YAAAhC,QAAA,eAEvB3E,OAAA,CAACF,SAAS;cAAC0F,QAAQ,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN5F,OAAA,CAACvB,OAAO;UAACsG,EAAE,EAAE;YAAEyB,EAAE,EAAE;UAAE;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAEzBrF,QAAQ,KAAK,MAAM,iBAClBP,OAAA,CAACf,WAAW;UAAC2H,SAAS;UAAC/B,IAAI,EAAC,OAAO;UAACE,EAAE,EAAE;YAAEyB,EAAE,EAAE;UAAE,CAAE;UAAA7B,QAAA,gBAChD3E,OAAA,CAAC5B,UAAU;YAACkI,OAAO,EAAC,OAAO;YAACvB,EAAE,EAAE;cAAEyB,EAAE,EAAE;YAAE,CAAE;YAAA7B,QAAA,EAAC;UAAe;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACvE5F,OAAA,CAACb,UAAU;YACT6D,KAAK,EAAE5B,UAAW;YAClByF,QAAQ,EAAEtD,sBAAuB;YACjCuD,GAAG;YAAAnC,QAAA,gBAEH3E,OAAA,CAACzB,gBAAgB;cACfyE,KAAK,EAAC,QAAQ;cACd+D,OAAO,eAAE/G,OAAA,CAACZ,KAAK;gBAACyF,IAAI,EAAC;cAAO;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAChCoB,KAAK,EAAC;YAAY;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACF5F,OAAA,CAACzB,gBAAgB;cACfyE,KAAK,EAAC,UAAU;cAChB+D,OAAO,eAAE/G,OAAA,CAACZ,KAAK;gBAACyF,IAAI,EAAC;cAAO;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAChCoB,KAAK,EAAC;YAAU;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACd,EAEArF,QAAQ,KAAK,QAAQ,iBACpBP,OAAA,CAACf,WAAW;UAAC2H,SAAS;UAAC/B,IAAI,EAAC,OAAO;UAACE,EAAE,EAAE;YAAEyB,EAAE,EAAE;UAAE,CAAE;UAAA7B,QAAA,gBAChD3E,OAAA,CAAC5B,UAAU;YAACkI,OAAO,EAAC,OAAO;YAACvB,EAAE,EAAE;cAAEyB,EAAE,EAAE;YAAE,CAAE;YAAA7B,QAAA,EAAC;UAAe;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACvE5F,OAAA,CAACb,UAAU;YACT6D,KAAK,EAAE5B,UAAW;YAClByF,QAAQ,EAAEtD,sBAAuB;YACjCwB,EAAE,EAAE;cAAE0B,OAAO,EAAE,MAAM;cAAEQ,aAAa,EAAE;YAAS,CAAE;YAAAtC,QAAA,gBAEjD3E,OAAA,CAACzB,gBAAgB;cACfyE,KAAK,EAAC,QAAQ;cACd+D,OAAO,eAAE/G,OAAA,CAACZ,KAAK;gBAACyF,IAAI,EAAC;cAAO;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAChCoB,KAAK,EAAC;YAAY;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACF5F,OAAA,CAACzB,gBAAgB;cACfyE,KAAK,EAAC,aAAa;cACnB+D,OAAO,eAAE/G,OAAA,CAACZ,KAAK;gBAACyF,IAAI,EAAC;cAAO;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAChCoB,KAAK,EAAC;YAAa;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACF5F,OAAA,CAACzB,gBAAgB;cACfyE,KAAK,EAAC,UAAU;cAChB+D,OAAO,eAAE/G,OAAA,CAACZ,KAAK;gBAACyF,IAAI,EAAC;cAAO;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAChCoB,KAAK,EAAC;YAAW;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACF5F,OAAA,CAACzB,gBAAgB;cACfyE,KAAK,EAAC,SAAS;cACf+D,OAAO,eAAE/G,OAAA,CAACZ,KAAK;gBAACyF,IAAI,EAAC;cAAO;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAChCoB,KAAK,EAAC;YAAc;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACd,EAEAxE,UAAU,KAAK,UAAU,iBACxBpB,OAAA,CAAC3B,SAAS;UACRuI,SAAS;UACT/B,IAAI,EAAC,OAAO;UACZmC,KAAK,EAAC,OAAO;UACbV,OAAO,EAAC,UAAU;UAClBtD,KAAK,EAAEnC,UAAW;UAClBgG,QAAQ,EAAE/D,YAAa;UACvBiC,EAAE,EAAE;YAAEyB,EAAE,EAAE;UAAE,CAAE;UACdU,UAAU,EAAE;YACVC,cAAc,eACZnH,OAAA,CAAClB,cAAc;cAACkG,QAAQ,EAAC,OAAO;cAAAL,QAAA,eAC9B3E,OAAA,CAACR,UAAU;gBAACgG,QAAQ,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,EAEAxE,UAAU,KAAK,aAAa,iBAC3BpB,OAAA,CAAC3B,SAAS;UACRuI,SAAS;UACT/B,IAAI,EAAC,OAAO;UACZmC,KAAK,EAAC,eAAe;UACrBV,OAAO,EAAC,UAAU;UAClBxC,IAAI,EAAC,QAAQ;UACbd,KAAK,EAAE1B,WAAW,CAACE,GAAI;UACvBqF,QAAQ,EAAGO,CAAC,IAAK5D,iBAAiB,CAAC,KAAK,EAAE4D,CAAC,CAACrE,MAAM,CAACC,KAAK,CAAE;UAC1D+B,EAAE,EAAE;YAAEyB,EAAE,EAAE;UAAE;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACF,EAEAxE,UAAU,KAAK,UAAU,iBACxBpB,OAAA,CAAC3B,SAAS;UACRuI,SAAS;UACT/B,IAAI,EAAC,OAAO;UACZmC,KAAK,EAAC,gBAAgB;UACtBV,OAAO,EAAC,UAAU;UAClBxC,IAAI,EAAC,QAAQ;UACbd,KAAK,EAAE1B,WAAW,CAACG,GAAI;UACvBoF,QAAQ,EAAGO,CAAC,IAAK5D,iBAAiB,CAAC,KAAK,EAAE4D,CAAC,CAACrE,MAAM,CAACC,KAAK,CAAE;UAC1D+B,EAAE,EAAE;YAAEyB,EAAE,EAAE;UAAE;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACF,EAEAxE,UAAU,KAAK,SAAS,iBACvBpB,OAAA,CAAC9B,GAAG;UAAC6G,EAAE,EAAE;YAAE0B,OAAO,EAAE,MAAM;YAAEY,GAAG,EAAE,CAAC;YAAEb,EAAE,EAAE;UAAE,CAAE;UAAA7B,QAAA,gBAC1C3E,OAAA,CAAC3B,SAAS;YACRuI,SAAS;YACT/B,IAAI,EAAC,OAAO;YACZmC,KAAK,EAAC,IAAI;YACVV,OAAO,EAAC,UAAU;YAClBxC,IAAI,EAAC,QAAQ;YACbd,KAAK,EAAE1B,WAAW,CAACE,GAAI;YACvBqF,QAAQ,EAAGO,CAAC,IAAK5D,iBAAiB,CAAC,KAAK,EAAE4D,CAAC,CAACrE,MAAM,CAACC,KAAK;UAAE;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACF5F,OAAA,CAAC3B,SAAS;YACRuI,SAAS;YACT/B,IAAI,EAAC,OAAO;YACZmC,KAAK,EAAC,GAAG;YACTV,OAAO,EAAC,UAAU;YAClBxC,IAAI,EAAC,QAAQ;YACbd,KAAK,EAAE1B,WAAW,CAACG,GAAI;YACvBoF,QAAQ,EAAGO,CAAC,IAAK5D,iBAAiB,CAAC,KAAK,EAAE4D,CAAC,CAACrE,MAAM,CAACC,KAAK;UAAE;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEAxE,UAAU,KAAK,QAAQ,iBACtBpB,OAAA,CAAAE,SAAA;UAAAyE,QAAA,gBACE3E,OAAA,CAAC3B,SAAS;YACRuI,SAAS;YACT/B,IAAI,EAAC,OAAO;YACZmC,KAAK,EAAC,cAAc;YACpBV,OAAO,EAAC,UAAU;YAClBtD,KAAK,EAAEnC,UAAW;YAClBgG,QAAQ,EAAE/D,YAAa;YACvBiC,EAAE,EAAE;cAAEyB,EAAE,EAAE;YAAE,CAAE;YACdU,UAAU,EAAE;cACVC,cAAc,eACZnH,OAAA,CAAClB,cAAc;gBAACkG,QAAQ,EAAC,OAAO;gBAAAL,QAAA,eAC9B3E,OAAA,CAACR,UAAU;kBAACgG,QAAQ,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAEpB,CAAE;YACFhE,QAAQ,EAAEA;UAAS;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAEF5F,OAAA,CAACzB,gBAAgB;YACfwI,OAAO,eACL/G,OAAA,CAAC1B,QAAQ;cACP4E,OAAO,EAAEhC,SAAU;cACnB2F,QAAQ,EAAE5D,eAAgB;cAC1B4B,IAAI,EAAC;YAAO;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;YACDoB,KAAK,EAAC,iBAAiB;YACvBjC,EAAE,EAAE;cAAEyB,EAAE,EAAE;YAAE;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF5F,OAAA,CAACvB,OAAO;YAACsG,EAAE,EAAE;cAAEyB,EAAE,EAAE;YAAE;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE1B5F,OAAA,CAACtB,IAAI;YAACqG,EAAE,EAAE;cAAEqB,SAAS,EAAE,OAAO;cAAEkB,QAAQ,EAAE,MAAM;cAAEd,EAAE,EAAE;YAAE,CAAE;YAAA7B,QAAA,GACvDD,oBAAoB,CAAC1C,GAAG,CAAC,CAACgB,KAAK,EAAEuE,KAAK,kBACrCvH,OAAA,CAACrB,QAAQ;cAAa6I,KAAK;cAACC,cAAc;cAAA9C,QAAA,eACxC3E,OAAA,CAACzB,gBAAgB;gBACfwI,OAAO,eACL/G,OAAA,CAAC1B,QAAQ;kBACP4E,OAAO,EAAElC,cAAc,CAACqC,QAAQ,CAACL,KAAK,CAAE;kBACxC6D,QAAQ,EAAEA,CAAA,KAAM1D,iBAAiB,CAACH,KAAK,CAAE;kBACzC6B,IAAI,EAAC;gBAAO;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CACF;gBACDoB,KAAK,eACHhH,OAAA,CAACpB,YAAY;kBACX8I,OAAO,EAAE1E,KAAK,IAAI,SAAU;kBAC5B2E,sBAAsB,EAAE;oBACtBrB,OAAO,EAAE,OAAO;oBAChBH,KAAK,EAAE;sBACLmB,QAAQ,EAAE,QAAQ;sBAClBM,YAAY,EAAE,UAAU;sBACxBC,UAAU,EAAE;oBACd;kBACF;gBAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF;gBACDb,EAAE,EAAE;kBAAE+C,CAAC,EAAE,CAAC;kBAAE1C,KAAK,EAAE;gBAAO;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC,GAvBW2B,KAAK;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwBV,CACX,CAAC,EACDlB,oBAAoB,CAAC7C,MAAM,KAAK,CAAC,iBAChC7B,OAAA,CAACrB,QAAQ;cAAAgG,QAAA,eACP3E,OAAA,CAACpB,YAAY;gBACX8I,OAAO,EAAC,uBAAuB;gBAC/BC,sBAAsB,EAAE;kBAAErB,OAAO,EAAE;gBAAQ;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CACX;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA,eACP,CACH,eAED5F,OAAA,CAAC9B,GAAG;UAAC6G,EAAE,EAAE;YAAE0B,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,UAAU;YAAEW,GAAG,EAAE;UAAE,CAAE;UAAA1C,QAAA,gBAC/D3E,OAAA,CAACxB,MAAM;YACL8H,OAAO,EAAC,UAAU;YAClBzB,IAAI,EAAC,OAAO;YACZD,OAAO,EAAE/B,WAAY;YAAA8B,QAAA,EACtB;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5F,OAAA,CAACxB,MAAM;YACL8H,OAAO,EAAC,WAAW;YACnBzB,IAAI,EAAC,OAAO;YACZD,OAAO,EAAEP,WAAY;YACrBS,KAAK,EAAC,SAAS;YAAAH,QAAA,EAChB;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAClF,EAAA,CAxdIP,eAAe;AAAA4H,EAAA,GAAf5H,eAAe;AA0drB,eAAeA,eAAe;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}